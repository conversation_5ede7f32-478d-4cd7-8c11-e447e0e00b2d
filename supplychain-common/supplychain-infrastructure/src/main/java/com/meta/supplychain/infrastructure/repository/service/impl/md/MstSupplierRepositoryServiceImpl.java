package com.meta.supplychain.infrastructure.repository.service.impl.md;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.dto.md.req.supplier.MstSupplierPageQueryReq;
import com.meta.supplychain.entity.po.md.MstSupplierPO;
import com.meta.supplychain.infrastructure.repository.mapper.md.MstSupplierMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierRepositoryService;
import org.springframework.stereotype.Service;

/**
 * 供应商RepositoryService实现类
 */
@Service
public class MstSupplierRepositoryServiceImpl extends ServiceImpl<MstSupplierMapper, MstSupplierPO> implements IMstSupplierRepositoryService {

    @Override
    public Page<MstSupplierPO> pageQuerySupplier(Page<MstSupplierPO> page, MstSupplierPageQueryReq request) {
        return baseMapper.pageQuerySupplier(page, request);
    }

    @Override
    public boolean updateSupplierByCode(MstSupplierPO supplierPO) {
        return lambdaUpdate()
                .eq(MstSupplierPO::getCode, supplierPO.getCode())
                // 允许NULL的字段 - 始终允许更新，包括设置为null
                .set(true, MstSupplierPO::getType, supplierPO.getType())
                .set(true, MstSupplierPO::getName, supplierPO.getName())
                .set(true, MstSupplierPO::getShortName, supplierPO.getShortName())
                .set(true, MstSupplierPO::getFax, supplierPO.getFax())
                .set(true, MstSupplierPO::getEmail, supplierPO.getEmail())
                .set(true, MstSupplierPO::getDownTime, supplierPO.getDownTime())
                .set(true, MstSupplierPO::getSuppCateCode, supplierPO.getSuppCateCode())
                .set(true, MstSupplierPO::getSuppCateName, supplierPO.getSuppCateName())
                .set(true, MstSupplierPO::getSuppType, supplierPO.getSuppType())
                .set(true, MstSupplierPO::getOperateMode, supplierPO.getOperateMode())
                .set(true, MstSupplierPO::getInvoiceType, supplierPO.getInvoiceType())
                .set(true, MstSupplierPO::getProvinceCode, supplierPO.getProvinceCode())
                .set(true, MstSupplierPO::getProvinceName, supplierPO.getProvinceName())
                .set(true, MstSupplierPO::getCityCode, supplierPO.getCityCode())
                .set(true, MstSupplierPO::getCityName, supplierPO.getCityName())
                .set(true, MstSupplierPO::getDistrictCode, supplierPO.getDistrictCode())
                .set(true, MstSupplierPO::getDistrictName, supplierPO.getDistrictName())
                .set(true, MstSupplierPO::getTaxNumber, supplierPO.getTaxNumber())
                .set(true, MstSupplierPO::getIbpsCode, supplierPO.getIbpsCode())
                .set(true, MstSupplierPO::getBankName, supplierPO.getBankName())
                .set(true, MstSupplierPO::getBankCode, supplierPO.getBankCode())
                .set(true, MstSupplierPO::getBankAccountName, supplierPO.getBankAccountName())
                .set(true, MstSupplierPO::getBankAccount, supplierPO.getBankAccount())
                .set(true, MstSupplierPO::getOutCode, supplierPO.getOutCode())
                .set(true, MstSupplierPO::getSource, supplierPO.getSource())
                .set(true, MstSupplierPO::getSettlementMethod, supplierPO.getSettlementMethod())
                .set(true, MstSupplierPO::getDirectOrderPushTime, supplierPO.getDirectOrderPushTime())
                .set(true, MstSupplierPO::getOrderCtrl, supplierPO.getOrderCtrl())
                .set(true, MstSupplierPO::getMergeDc, supplierPO.getMergeDc())
                .set(true, MstSupplierPO::getEsign, supplierPO.getEsign())
                .set(true, MstSupplierPO::getEinv, supplierPO.getEinv())
                .set(true, MstSupplierPO::getSuppConf, supplierPO.getSuppConf())
                .set(true, MstSupplierPO::getEntType, supplierPO.getEntType())
                .set(true, MstSupplierPO::getSuppBussinesType, supplierPO.getSuppBussinesType())
                .set(true, MstSupplierPO::getLegalPersonName, supplierPO.getLegalPersonName())
                .set(true, MstSupplierPO::getLegalCertType, supplierPO.getLegalCertType())
                .set(true, MstSupplierPO::getLegalCertNumber, supplierPO.getLegalCertNumber())
                .set(true, MstSupplierPO::getLegalPhone, supplierPO.getLegalPhone())
                .set(true, MstSupplierPO::getRegisteredCapital, supplierPO.getRegisteredCapital())
                .set(true, MstSupplierPO::getEnterpriseName, supplierPO.getEnterpriseName())
                .set(true, MstSupplierPO::getBusinessScope, supplierPO.getBusinessScope())
                .set(true, MstSupplierPO::getMerchantCategory, supplierPO.getMerchantCategory())
                .set(true, MstSupplierPO::getRemark, supplierPO.getRemark())
                .set(true, MstSupplierPO::getCreditCode, supplierPO.getCreditCode())
                .set(true, MstSupplierPO::getInvoiceCategory, supplierPO.getInvoiceCategory())
                .set(true, MstSupplierPO::getInvoiceAddress, supplierPO.getInvoiceAddress())
                .set(true, MstSupplierPO::getRecipientAddress, supplierPO.getRecipientAddress())
                .set(true, MstSupplierPO::getRecipientName, supplierPO.getRecipientName())
                .set(true, MstSupplierPO::getRecipientPhone, supplierPO.getRecipientPhone())
                .set(true, MstSupplierPO::getPostalCode, supplierPO.getPostalCode())
                .set(true, MstSupplierPO::getSuspendBusinessTime, supplierPO.getSuspendBusinessTime())
                .set(true, MstSupplierPO::getClearanceTime, supplierPO.getClearanceTime())
                .set(true, MstSupplierPO::getInvoicePhone, supplierPO.getInvoicePhone())
                .set(true, MstSupplierPO::getCategoryCode, supplierPO.getCategoryCode())
                .set(true, MstSupplierPO::getCategoryName, supplierPO.getCategoryName())
                .set(true, MstSupplierPO::getCategoryItemCode, supplierPO.getCategoryItemCode())
                .set(true, MstSupplierPO::getInvoiceBankName, supplierPO.getInvoiceBankName())
                .set(true, MstSupplierPO::getInvoiceBankAccount, supplierPO.getInvoiceBankAccount())
                // NOT NULL字段 - 只有当传入值不为null时才更新
                .set(supplierPO.getLinkMan() != null, MstSupplierPO::getLinkMan, supplierPO.getLinkMan())
                .set(supplierPO.getPhone() != null, MstSupplierPO::getPhone, supplierPO.getPhone())
                .set(supplierPO.getStatus() != null, MstSupplierPO::getStatus, supplierPO.getStatus())
                .set(supplierPO.getPaymentControl() != null, MstSupplierPO::getPaymentControl, supplierPO.getPaymentControl())
                .set(supplierPO.getOperateControl() != null, MstSupplierPO::getOperateControl, supplierPO.getOperateControl())
                .set(supplierPO.getSettleMode() != null, MstSupplierPO::getSettleMode, supplierPO.getSettleMode())
                .set(supplierPO.getSuppTaxRate() != null, MstSupplierPO::getSuppTaxRate, supplierPO.getSuppTaxRate())
                .set(supplierPO.getAddress() != null, MstSupplierPO::getAddress, supplierPO.getAddress())
                .update();
    }
}
