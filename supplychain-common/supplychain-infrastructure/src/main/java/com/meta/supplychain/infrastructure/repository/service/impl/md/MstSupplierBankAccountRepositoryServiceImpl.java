package com.meta.supplychain.infrastructure.repository.service.impl.md;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.md.MstSupplierBankAccountPO;
import com.meta.supplychain.infrastructure.repository.mapper.md.MstSupplierBankAccountMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierBankAccountRepositoryService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商家银行账户RepositoryService实现类
 */
@Service
public class MstSupplierBankAccountRepositoryServiceImpl extends ServiceImpl<MstSupplierBankAccountMapper, MstSupplierBankAccountPO> implements IMstSupplierBankAccountRepositoryService {

    @Override
    public List<MstSupplierBankAccountPO> listBySupplierCode(String supplierCode) {
        return list(new LambdaQueryWrapper<MstSupplierBankAccountPO>()
                .eq(MstSupplierBankAccountPO::getSupplierCode, supplierCode));
    }
}
