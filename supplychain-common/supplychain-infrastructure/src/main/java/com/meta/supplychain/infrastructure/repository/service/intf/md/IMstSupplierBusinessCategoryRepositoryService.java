package com.meta.supplychain.infrastructure.repository.service.intf.md;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.entity.po.md.MstSupplierBusinessCategoryPO;

import java.util.List;

public interface IMstSupplierBusinessCategoryRepositoryService extends IService<MstSupplierBusinessCategoryPO> {

    /**
     * 根据供应商编码查询经营品类信息
     *
     * @param supplierCode 供应商编码
     * @return 经营品类信息列表
     */
    List<MstSupplierBusinessCategoryPO> listBySupplierCode(String supplierCode);
}
