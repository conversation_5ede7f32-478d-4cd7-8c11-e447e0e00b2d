package com.meta.supplychain.infrastructure.repository.service.impl.md;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.md.MstSupplierBusinessBrandPO;
import com.meta.supplychain.infrastructure.repository.mapper.md.MstSupplierBusinessBrandMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierBusinessBrandRepositoryService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商家经营品牌RepositoryService实现类
 */
@Service
public class MstSupplierBusinessBrandRepositoryServiceImpl extends ServiceImpl<MstSupplierBusinessBrandMapper, MstSupplierBusinessBrandPO> implements IMstSupplierBusinessBrandRepositoryService {

    @Override
    public List<MstSupplierBusinessBrandPO> listBySupplierCode(String supplierCode) {
        return list(new LambdaQueryWrapper<MstSupplierBusinessBrandPO>()
                .eq(MstSupplierBusinessBrandPO::getSupplierCode, supplierCode)
                .eq(MstSupplierBusinessBrandPO::getDelFlag, 0));
    }
}
