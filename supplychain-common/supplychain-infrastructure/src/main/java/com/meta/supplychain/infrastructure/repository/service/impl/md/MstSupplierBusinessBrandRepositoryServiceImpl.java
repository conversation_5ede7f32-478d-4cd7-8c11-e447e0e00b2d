package com.meta.supplychain.infrastructure.repository.service.impl.md;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.md.MstSupplierBusinessBrandPO;
import com.meta.supplychain.infrastructure.repository.mapper.md.MstSupplierBusinessBrandMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierBusinessBrandRepositoryService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商家经营品牌RepositoryService实现类
 */
@Service
public class MstSupplierBusinessBrandRepositoryServiceImpl extends ServiceImpl<MstSupplierBusinessBrandMapper, MstSupplierBusinessBrandPO> implements IMstSupplierBusinessBrandRepositoryService {

    @Override
    public List<MstSupplierBusinessBrandPO> listBySupplierCode(String supplierCode) {
        return list(Wrappers.<MstSupplierBusinessBrandPO>lambdaQuery()
                .eq(MstSupplierBusinessBrandPO::getSupplierCode, supplierCode));
    }
}
