package com.meta.supplychain.infrastructure.repository.service.intf.md;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.entity.po.md.MstSupplierBusinessContactPO;

import java.util.List;

public interface IMstSupplierBusinessContactRepositoryService extends IService<MstSupplierBusinessContactPO> {

    /**
     * 根据供应商编码查询联系人信息
     *
     * @param supplierCode 供应商编码
     * @return 联系人信息列表
     */
    List<MstSupplierBusinessContactPO> listBySupplierCode(String supplierCode);
}
