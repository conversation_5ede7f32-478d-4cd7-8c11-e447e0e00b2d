package com.meta.supplychain.infrastructure.repository.service.intf.md;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.entity.po.md.MstSupplierBankAccountPO;

import java.util.List;

public interface IMstSupplierBankAccountRepositoryService extends IService<MstSupplierBankAccountPO> {

    /**
     * 根据供应商编码查询银行账户信息
     *
     * @param supplierCode 供应商编码
     * @return 银行账户信息列表
     */
    List<MstSupplierBankAccountPO> listBySupplierCode(String supplierCode);
}
