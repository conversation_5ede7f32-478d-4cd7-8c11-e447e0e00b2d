package com.meta.supplychain.infrastructure.repository.service.impl.md;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.md.MstSupplierBusinessContactPO;
import com.meta.supplychain.infrastructure.repository.mapper.md.MstSupplierBusinessContactMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierBusinessContactRepositoryService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商家经营联系人RepositoryService实现类
 */
@Service
public class MstSupplierBusinessContactRepositoryServiceImpl extends ServiceImpl<MstSupplierBusinessContactMapper, MstSupplierBusinessContactPO> implements IMstSupplierBusinessContactRepositoryService {

    @Override
    public List<MstSupplierBusinessContactPO> listBySupplierCode(String supplierCode) {
        return list(new LambdaQueryWrapper<MstSupplierBusinessContactPO>()
                .eq(MstSupplierBusinessContactPO::getSupplierCode, supplierCode)
                .eq(MstSupplierBusinessContactPO::getDelFlag, 0));
    }
}
