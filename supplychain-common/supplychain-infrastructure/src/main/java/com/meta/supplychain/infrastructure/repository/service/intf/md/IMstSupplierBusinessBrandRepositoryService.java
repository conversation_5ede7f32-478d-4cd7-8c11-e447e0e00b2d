package com.meta.supplychain.infrastructure.repository.service.intf.md;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.entity.po.md.MstSupplierBusinessBrandPO;

import java.util.List;

public interface IMstSupplierBusinessBrandRepositoryService extends IService<MstSupplierBusinessBrandPO> {

    /**
     * 根据供应商编码查询经营品牌信息
     *
     * @param supplierCode 供应商编码
     * @return 经营品牌信息列表
     */
    List<MstSupplierBusinessBrandPO> listBySupplierCode(String supplierCode);
}
