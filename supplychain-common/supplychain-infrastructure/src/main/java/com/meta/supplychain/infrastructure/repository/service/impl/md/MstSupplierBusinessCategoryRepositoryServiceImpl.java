package com.meta.supplychain.infrastructure.repository.service.impl.md;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.po.md.MstSupplierBusinessCategoryPO;
import com.meta.supplychain.infrastructure.repository.mapper.md.MstSupplierBusinessCategoryMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierBusinessCategoryRepositoryService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商家经营品类RepositoryService实现类
 */
@Service
public class MstSupplierBusinessCategoryRepositoryServiceImpl extends ServiceImpl<MstSupplierBusinessCategoryMapper, MstSupplierBusinessCategoryPO> implements IMstSupplierBusinessCategoryRepositoryService {

    @Override
    public List<MstSupplierBusinessCategoryPO> listBySupplierCode(String supplierCode) {
        return list(Wrappers.<MstSupplierBusinessCategoryPO>lambdaQuery()
                .eq(MstSupplierBusinessCategoryPO::getSupplierCode, supplierCode));
    }
}
