package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.supplier.MstSupplierBusinessCategoryDTO;
import com.meta.supplychain.entity.po.md.MstSupplierBusinessCategoryPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 商家经营品类关联转换器
 */
@Mapper
public interface MstSupplierBusinessCategoryConvert {

    MstSupplierBusinessCategoryConvert INSTANCE = Mappers.getMapper(MstSupplierBusinessCategoryConvert.class);

    /**
     * PO转DTO
     */
    MstSupplierBusinessCategoryDTO po2Dto(MstSupplierBusinessCategoryPO po);

    /**
     * PO列表转DTO列表
     */
    List<MstSupplierBusinessCategoryDTO> po2DtoList(List<MstSupplierBusinessCategoryPO> poList);

    /**
     * DTO转PO
     * 忽略BaseEntity审计字段，这些字段由MyBatis-Plus自动填充
     */
    @Mapping(target = "createCode", ignore = true)
    @Mapping(target = "createUid", ignore = true)
    @Mapping(target = "createName", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateUid", ignore = true)
    @Mapping(target = "updateCode", ignore = true)
    @Mapping(target = "updateName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    MstSupplierBusinessCategoryPO dto2Po(MstSupplierBusinessCategoryDTO dto);
}
