package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.supplier.MstSupplierDTO;
import com.meta.supplychain.entity.po.md.MstSupplierPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 供应商转换器
 */
@Mapper
public interface MstSupplierConvert {

    MstSupplierConvert INSTANCE = Mappers.getMapper(MstSupplierConvert.class);

    /**
     * PO转DTO
     */
    MstSupplierDTO po2Dto(MstSupplierPO po);

    /**
     * PO列表转DTO列表
     */
    List<MstSupplierDTO> po2DtoList(List<MstSupplierPO> poList);

    /**
     * DTO转PO
     * 忽略BaseEntity审计字段，这些字段由MyBatis-Plus自动填充
     */
    @Mapping(target = "createCode", ignore = true)
    @Mapping(target = "createUid", ignore = true)
    @Mapping(target = "createName", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateUid", ignore = true)
    @Mapping(target = "updateCode", ignore = true)
    @Mapping(target = "updateName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    MstSupplierPO dto2Po(MstSupplierDTO dto);
}
