package com.meta.supplychain.entity.po.md;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meta.supplychain.entity.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 商家银行财务信息表
 * @TableName mst_supplier_bank_account
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "mst_supplier_bank_account")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MstSupplierBankAccountPO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 商家code
     */
    private String supplierCode;

    /**
     * 是否为主账号
     */
    private Integer isMainAccount;

    /**
     * 开户银行：银行档案
     */
    private String bankCode;

    /**
     * 开户网点（银行全称）：银行网点档案
     */
    private String bankBranchCode;

    /**
     * 联行号：银行网点档案自带
     */
    private String unionBankCode;

    /**
     * 银行账户名称
     */
    private String accountName;

    /**
     * 银行账号
     */
    private String accountNumber;

    /**
     * 租户号
     */
    private Long tenantId;

    /**
     * 逻辑删除标记：0-正常，1-已删除
     */
    private Integer delFlag;
}
