package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.supplier.MstSupplierBusinessBrandDTO;
import com.meta.supplychain.entity.po.md.MstSupplierBusinessBrandPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 商家经营品牌关联转换器
 */
@Mapper
public interface MstSupplierBusinessBrandConvert {

    MstSupplierBusinessBrandConvert INSTANCE = Mappers.getMapper(MstSupplierBusinessBrandConvert.class);

    /**
     * PO转DTO
     */
    MstSupplierBusinessBrandDTO po2Dto(MstSupplierBusinessBrandPO po);

    /**
     * PO列表转DTO列表
     */
    List<MstSupplierBusinessBrandDTO> po2DtoList(List<MstSupplierBusinessBrandPO> poList);

    /**
     * DTO转PO
     * 忽略BaseEntity审计字段，这些字段由MyBatis-Plus自动填充
     */
    @Mapping(target = "createCode", ignore = true)
    @Mapping(target = "createUid", ignore = true)
    @Mapping(target = "createName", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateUid", ignore = true)
    @Mapping(target = "updateCode", ignore = true)
    @Mapping(target = "updateName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    MstSupplierBusinessBrandPO dto2Po(MstSupplierBusinessBrandDTO dto);
}
