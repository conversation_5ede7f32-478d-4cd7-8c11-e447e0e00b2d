package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.supplier.MstSupplierBusinessBrandDTO;
import com.meta.supplychain.entity.po.md.MstSupplierBusinessBrandPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 商家经营品牌关联转换器
 */
@Mapper
public interface MstSupplierBusinessBrandConvert {

    MstSupplierBusinessBrandConvert INSTANCE = Mappers.getMapper(MstSupplierBusinessBrandConvert.class);

    /**
     * PO转DTO
     */
    MstSupplierBusinessBrandDTO po2Dto(MstSupplierBusinessBrandPO po);

    /**
     * PO列表转DTO列表
     */
    List<MstSupplierBusinessBrandDTO> po2DtoList(List<MstSupplierBusinessBrandPO> poList);

    /**
     * DTO转PO
     */
    MstSupplierBusinessBrandPO dto2Po(MstSupplierBusinessBrandDTO dto);
}
