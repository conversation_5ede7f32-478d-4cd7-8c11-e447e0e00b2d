package com.meta.supplychain.entity.dto.md.supplier;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商家经营品类关联DTO
 */
@Data
@Schema(description = "商家经营品类关联信息")
public class MstSupplierBusinessCategoryDTO {

    @Schema(description = "主键ID")
    private Long id;

    @NotNull(message = "商家编码不可为空")
    @Schema(description = "商家编码")
    private Long supplierCode;

    @NotBlank(message = "品类编码不可为空")
    @Length(max = 128, message = "品类编码长度不能超过128")
    @Schema(description = "品类编码")
    private String categoryCode;

    @Length(max = 255, message = "品类名称长度不能超过255")
    @Schema(description = "品类名称")
    private String categoryName;

    @Length(max = 128, message = "品类项编码长度不能超过128")
    @Schema(description = "品类项编码")
    private String categoryItemCode;

    @Schema(description = "租户号")
    private Long tenantId;

    @Schema(description = "逻辑删除标记：0-正常，1-已删除")
    private Integer delFlag;
}
