package com.meta.supplychain.entity.po.md;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meta.supplychain.entity.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 商家经营类目关联表
 * @TableName mst_supplier_business_category
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "mst_supplier_business_category")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MstSupplierBusinessCategoryPO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 商家code
     */
    private String supplierCode;

    /**
     * 品类编码
     */
    private String categoryCode;

    /**
     * 品类名称
     */
    private String categoryName;

    /**
     * 品类项编码
     */
    private String categoryItemCode;

    /**
     * 租户号
     */
    private Long tenantId;

    /**
     * 逻辑删除标记：0-正常，1-已删除
     */
    private Integer delFlag;
}
