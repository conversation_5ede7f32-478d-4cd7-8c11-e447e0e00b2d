package com.meta.supplychain.entity.dto.md.supplier;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.supplychain.enums.md.*;
import com.meta.supplychain.serializes.LocalDatetimeDeserializer;
import com.meta.supplychain.serializes.LocalDatetimeSerializer;
import com.meta.supplychain.validation.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 供应商DTO
 */
@Data
@Schema(description = "供应商信息")
public class MstSupplierDTO {

    @Schema(description = "主键ID")
    private Long id;

    @EnumValue(type = MstSupplierTypeEnum.class)
    @Schema(description = "商家类型 1 供应商；2 pop联营；3 pop自营")
    private Integer type;

    @NotBlank(message = "商家编码不可为空")
    @Length(max = 20, message = "商家编码长度不能超过20")
    @Schema(description = "商家编码")
    private String code;

    @Length(max = 150, message = "商家名称长度不能超过150")
    @Schema(description = "商家名称")
    private String name;

    @Length(max = 64, message = "商家简称长度不能超过64")
    @Schema(description = "商家简称")
    private String shortName;

    @NotBlank(message = "联系人姓名不可为空")
    @Length(max = 50, message = "联系人姓名长度不能超过50")
    @Schema(description = "联系人姓名")
    private String linkMan;

    @Length(max = 64, message = "联系人职务长度不能超过64")
    @Schema(description = "联系人职务")
    private String linkManTitle;

    @Length(max = 64, message = "联系人邮箱长度不能超过64")
    @Schema(description = "联系人邮箱")
    private String linkManEmail;

    @Length(max = 64, message = "联系人身份证号长度不能超过64")
    @Schema(description = "联系人身份证号")
    private String linkManIdCard;

    @Length(max = 32, message = "联系人办公电话长度不能超过32")
    @Schema(description = "联系人办公电话")
    private String linkManContactPhone;

    @NotBlank(message = "联系人手机号不可为空")
    @Length(max = 30, message = "联系人手机号长度不能超过30")
    @Schema(description = "联系人手机号")
    private String phone;

    @Length(max = 30, message = "办公电话长度不能超过30")
    @Schema(description = "办公电话")
    private String contactPhone;

    @Length(max = 32, message = "传真长度不能超过32")
    @Schema(description = "传真")
    private String fax;

    @Length(max = 64, message = "邮箱长度不能超过64")
    @Schema(description = "邮箱")
    private String email;

    @NotNull(message = "启用状态不可为空")
    @EnumValue(type = MstSupplierStatusEnum.class, required = true)
    @Schema(description = "启用状态 1启用；0停用")
    private Integer status;

    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "停用时间")
    private LocalDateTime downTime;

    @Length(max = 50, message = "商家分类编码长度不能超过50")
    @Schema(description = "商家分类编码")
    private String suppCateCode;

    @Length(max = 50, message = "商家分类名称长度不能超过50")
    @Schema(description = "商家分类名称")
    private String suppCateName;

    @Schema(description = "供应商类型")
    private Integer suppType;

    @EnumValue(type = MstSupplierOperateModeEnum.class)
    @Length(max = 10, message = "经营方式长度不能超过10")
    @Schema(description = "经营方式 J经销；D代销；L联营；Z租赁")
    private String operateMode;

    @NotNull(message = "付款控制不可为空")
    @EnumValue(type = MstSupplierPaymentControlEnum.class, required = true)
    @Schema(description = "付款控制: 1允许付款 ；2不允许付款 ；3不允许结算")
    private Integer paymentControl;

    @NotNull(message = "经营控制不可为空")
    @EnumValue(type = MstSupplierOperateControlEnum.class, required = true)
    @Schema(description = "经营控制: 1不控制 ；2暂停进货 ；3清场")
    private Integer operateControl;

    @NotNull(message = "结算模式不可为空")
    @EnumValue(type = MstSupplierSettleModeEnum.class, required = true)
    @Schema(description = "结算模式: 1统一结算 ；2本地结算 ；3区域结算")
    private Integer settleMode;

    @EnumValue(type = MstSupplierInvoiceTypeEnum.class)
    @Schema(description = "纳税人类型: 1一般纳税人； 2小规模纳税人")
    private Integer invoiceType;

    @NotBlank(message = "供应商税率不可为空")
    @Length(max = 50, message = "供应商税率长度不能超过50")
    @Schema(description = "供应商税率")
    private String suppTaxRate;

    @Length(max = 50, message = "省编码长度不能超过50")
    @Schema(description = "省编码")
    private String provinceCode;

    @Length(max = 50, message = "省名称长度不能超过50")
    @Schema(description = "省名称")
    private String provinceName;

    @Length(max = 50, message = "市编码长度不能超过50")
    @Schema(description = "市编码")
    private String cityCode;

    @Length(max = 50, message = "市名称长度不能超过50")
    @Schema(description = "市名称")
    private String cityName;

    @Length(max = 50, message = "区编码长度不能超过50")
    @Schema(description = "区编码")
    private String districtCode;

    @Length(max = 50, message = "区名称长度不能超过50")
    @Schema(description = "区名称")
    private String districtName;

    @NotBlank(message = "详细地址不可为空")
    @Length(max = 255, message = "详细地址长度不能超过255")
    @Schema(description = "详细地址")
    private String address;

    @Length(max = 50, message = "纳税人识别号长度不能超过50")
    @Schema(description = "纳税人识别号")
    private String taxNumber;

    @Length(max = 50, message = "银联号长度不能超过50")
    @Schema(description = "银联号")
    private String ibpsCode;

    @Length(max = 50, message = "银行类型长度不能超过50")
    @Schema(description = "银行类型")
    private String bankName;

    @Length(max = 50, message = "联行号长度不能超过50")
    @Schema(description = "联行号")
    private String bankCode;

    @Length(max = 100, message = "开户名称长度不能超过100")
    @Schema(description = "开户名称")
    private String bankAccountName;

    @Length(max = 100, message = "开户名称全称长度不能超过100")
    @Schema(description = "开户名称全称")
    private String bankAccount;

    @Schema(description = "逻辑删除 1删除；0正常")
    private Integer delFlag;

    @Schema(description = "租户号")
    private Long tenantId;

    @Length(max = 20, message = "外部编码长度不能超过20")
    @Schema(description = "外部编码")
    private String outCode;

    @EnumValue(type = MstSupplierSourceEnum.class)
    @Schema(description = "来源 1页面新增；2接口同步")
    private Integer source;

    @EnumValue(type = MstSupplierSettlementMethodEnum.class)
    @Schema(description = "结算方法 1账期结算；2预付款；3其他；4现采现结")
    private Integer settlementMethod;

    @Length(max = 512, message = "代发订单推送时间长度不能超过512")
    @Schema(description = "代发订单推送时间,英文逗号分割,0030,0050")
    private String directOrderPushTime;

    @Schema(description = "是否订单控制：0-否，1-是")
    private Integer orderCtrl;

    @Schema(description = "是否合并直流订单：0-否，1-是")
    private Integer mergeDc;

    @Schema(description = "是否启用电子签：0-否，1-是")
    private Integer esign;

    @Schema(description = "是否启用数电票：0-否，1-是")
    private Integer einv;

    @Schema(description = "是否启用订单供应商确认：0-否，1-是")
    private Integer suppConf;

    @EnumValue(type = MstSupplierEntTypeEnum.class)
    @Schema(description = "企业性质 1 代理商；2生产商；3批发商；4其他")
    private Integer entType;

    @Length(max = 32, message = "企业类型编码长度不能超过32")
    @Schema(description = "企业类型编码（数据字典扩展字段）")
    private String corpType;

    @EnumValue(type = MstSupplierBusinessTypeEnum.class)
    @Schema(description = "商家性质 1 个体工商户；2 公司")
    private Integer suppBussinesType;

    @Length(max = 50, message = "法人代表长度不能超过50")
    @Schema(description = "法人代表")
    private String legalPersonName;

    @EnumValue(type = MstSupplierLegalCertTypeEnum.class)
    @Schema(description = "法人证件类型：1居民身份证")
    private Integer legalCertType;

    @Length(max = 30, message = "法人证件号码长度不能超过30")
    @Schema(description = "法人证件号码")
    private String legalCertNumber;

    @Length(max = 30, message = "法人手机号长度不能超过30")
    @Schema(description = "法人手机号")
    private String legalPhone;

    @Schema(description = "注册资本")
    private BigDecimal registeredCapital;

    @Length(max = 255, message = "注册资本金额单位描述长度不能超过255")
    @Schema(description = "注册资本金额单位描述")
    private String registeredCapitalUnit;

    @Length(max = 150, message = "企业名称长度不能超过150")
    @Schema(description = "企业名称")
    private String enterpriseName;

    @Length(max = 500, message = "经营范围长度不能超过500")
    @Schema(description = "经营范围")
    private String businessScope;

    @Length(max = 64, message = "商家类别长度不能超过64")
    @Schema(description = "商家类别（数据字典，租户可自定义枚举）")
    private String merchantCategory;

    @Length(max = 255, message = "备注长度不能超过255")
    @Schema(description = "备注")
    private String remark;

    @Length(max = 30, message = "统一信用代码长度不能超过30")
    @Schema(description = "统一信用代码")
    private String creditCode;

    @EnumValue(type = MstSupplierInvoiceCategoryEnum.class)
    @Schema(description = "发票类型：1-专票，2-普票，3-收据")
    private Integer invoiceCategory;

    @Length(max = 255, message = "注册地址长度不能超过255")
    @Schema(description = "注册地址")
    private String invoiceAddress;

    @Length(max = 255, message = "收件人地址长度不能超过255")
    @Schema(description = "收件人地址")
    private String recipientAddress;

    @Length(max = 50, message = "收件人姓名长度不能超过50")
    @Schema(description = "收件人姓名")
    private String recipientName;

    @Length(max = 20, message = "收件人电话长度不能超过20")
    @Schema(description = "收件人电话")
    private String recipientPhone;

    @Length(max = 10, message = "邮政编码长度不能超过10")
    @Schema(description = "邮政编码")
    private String postalCode;

    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "暂停经营时间")
    private LocalDateTime suspendBusinessTime;

    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "清场时间")
    private LocalDateTime clearanceTime;

    @Length(max = 20, message = "开票电话长度不能超过20")
    @Schema(description = "开票电话")
    private String invoicePhone;

    @Length(max = 255, message = "主营类目（品类编码）长度不能超过255")
    @Schema(description = "主营类目（品类编码）")
    private String categoryCode;

    @Length(max = 255, message = "主营类目（品类名称）长度不能超过255")
    @Schema(description = "主营类目（品类名称）")
    private String categoryName;

    @Length(max = 255, message = "主营类目（品类项编码）长度不能超过255")
    @Schema(description = "主营类目（品类项编码）")
    private String categoryItemCode;

    @Length(max = 32, message = "注册电话长度不能超过32")
    @Schema(description = "注册电话")
    private String registeredPhone;

    // ==================== BaseEntity 审计字段 ====================

    @Schema(description = "创建人编码")
    private String createCode;

    @Schema(description = "创建人ID")
    private Long createUid;

    @Schema(description = "创建人姓名")
    private String createName;

    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改人ID")
    private Long updateUid;

    @Schema(description = "修改人编码")
    private String updateCode;

    @Schema(description = "修改人姓名")
    private String updateName;

    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    // ==================== 关联表信息 ====================

    @Schema(description = "银行账户信息列表")
    private List<MstSupplierBankAccountDTO> bankAccounts;

    @Schema(description = "经营品牌信息列表")
    private List<MstSupplierBusinessBrandDTO> businessBrands;

    @Schema(description = "经营品类信息列表")
    private List<MstSupplierBusinessCategoryDTO> businessCategories;

    @Schema(description = "联系人信息列表")
    private List<MstSupplierBusinessContactDTO> businessContacts;
}
