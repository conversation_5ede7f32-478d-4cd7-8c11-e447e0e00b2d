package com.meta.supplychain.entity.po.md;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meta.supplychain.entity.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 商家经营品牌关联表
 * @TableName mst_supplier_business_brand
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "mst_supplier_business_brand")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MstSupplierBusinessBrandPO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 商家编码
     */
    private String supplierCode;

    /**
     * 品牌编码
     */
    private String brandCode;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 是否自有品牌：0-否，1-是
     */
    private Integer selfOwned;

    /**
     * 租户号
     */
    private Long tenantId;

    /**
     * 逻辑删除标记：0-正常，1-已删除
     */
    private Integer delFlag;
}
