package com.meta.supplychain.entity.dto.md.supplier;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 商家银行财务信息DTO
 */
@Data
@Schema(description = "商家银行财务信息")
public class MstSupplierBankAccountDTO {

    @Schema(description = "主键ID")
    private Long id;

    @NotBlank(message = "商家编码不可为空")
    @Length(max = 20, message = "商家编码长度不能超过20")
    @Schema(description = "商家编码")
    private String supplierCode;

    @Schema(description = "是否为主账号")
    private Integer isMainAccount;

    @NotBlank(message = "开户银行不可为空")
    @Length(max = 50, message = "开户银行长度不能超过50")
    @Schema(description = "开户银行：银行档案")
    private String bankCode;

    @NotBlank(message = "开户网点不可为空")
    @Length(max = 50, message = "开户网点长度不能超过50")
    @Schema(description = "开户网点（银行全称）：银行网点档案")
    private String bankBranchCode;

    @Length(max = 30, message = "联行号长度不能超过30")
    @Schema(description = "联行号：银行网点档案自带")
    private String unionBankCode;

    @NotBlank(message = "银行账户名称不可为空")
    @Length(max = 100, message = "银行账户名称长度不能超过100")
    @Schema(description = "银行账户名称")
    private String accountName;

    @NotBlank(message = "银行账号不可为空")
    @Length(max = 50, message = "银行账号长度不能超过50")
    @Schema(description = "银行账号")
    private String accountNumber;

    @Schema(description = "租户号")
    private Long tenantId;

    @Schema(description = "逻辑删除标记：0-正常，1-已删除")
    private Integer delFlag;
}
