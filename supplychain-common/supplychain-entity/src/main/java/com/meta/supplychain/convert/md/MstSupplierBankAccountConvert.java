package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.supplier.MstSupplierBankAccountDTO;
import com.meta.supplychain.entity.po.md.MstSupplierBankAccountPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 商家银行账户转换器
 */
@Mapper
public interface MstSupplierBankAccountConvert {

    MstSupplierBankAccountConvert INSTANCE = Mappers.getMapper(MstSupplierBankAccountConvert.class);

    /**
     * PO转DTO
     */
    MstSupplierBankAccountDTO po2Dto(MstSupplierBankAccountPO po);

    /**
     * PO列表转DTO列表
     */
    List<MstSupplierBankAccountDTO> po2DtoList(List<MstSupplierBankAccountPO> poList);

    /**
     * DTO转PO
     * 忽略BaseEntity审计字段，这些字段由MyBatis-Plus自动填充
     */
    @Mapping(target = "createCode", ignore = true)
    @Mapping(target = "createUid", ignore = true)
    @Mapping(target = "createName", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateUid", ignore = true)
    @Mapping(target = "updateCode", ignore = true)
    @Mapping(target = "updateName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    MstSupplierBankAccountPO dto2Po(MstSupplierBankAccountDTO dto);
}
