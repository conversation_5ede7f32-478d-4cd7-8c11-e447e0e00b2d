package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.supplier.MstSupplierBankAccountDTO;
import com.meta.supplychain.entity.po.md.MstSupplierBankAccountPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 商家银行账户转换器
 */
@Mapper
public interface MstSupplierBankAccountConvert {

    MstSupplierBankAccountConvert INSTANCE = Mappers.getMapper(MstSupplierBankAccountConvert.class);

    /**
     * PO转DTO
     */
    MstSupplierBankAccountDTO po2Dto(MstSupplierBankAccountPO po);

    /**
     * PO列表转DTO列表
     */
    List<MstSupplierBankAccountDTO> po2DtoList(List<MstSupplierBankAccountPO> poList);

    /**
     * DTO转PO
     */
    MstSupplierBankAccountPO dto2Po(MstSupplierBankAccountDTO dto);
}
