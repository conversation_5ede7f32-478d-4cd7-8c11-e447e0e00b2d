package com.meta.supplychain.entity.dto.md.supplier;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 商家经营联系人DTO
 */
@Data
@Schema(description = "商家经营联系人信息")
public class MstSupplierBusinessContactDTO {

    @Schema(description = "主键ID")
    private Long id;

    @NotBlank(message = "商家编码不可为空")
    @Length(max = 20, message = "商家编码长度不能超过20")
    @Schema(description = "商家编码")
    private String supplierCode;

    @NotBlank(message = "联系人姓名不可为空")
    @Length(max = 50, message = "联系人姓名长度不能超过50")
    @Schema(description = "联系人姓名")
    private String contactName;

    @Length(max = 100, message = "联系人职务长度不能超过100")
    @Schema(description = "联系人职务")
    private String contactPosition;

    @NotBlank(message = "联系人手机号不可为空")
    @Length(max = 20, message = "联系人手机号长度不能超过20")
    @Schema(description = "联系人手机号")
    private String contactMobile;

    @Length(max = 20, message = "联系人办公电话长度不能超过20")
    @Schema(description = "联系人办公电话")
    private String contactPhone;

    @Length(max = 100, message = "联系人邮箱长度不能超过100")
    @Schema(description = "联系人邮箱")
    private String contactEmail;

    @Length(max = 30, message = "联系人身份证号长度不能超过30")
    @Schema(description = "联系人身份证号")
    private String contactIdCard;

    @Length(max = 255, message = "备注长度不能超过255")
    @Schema(description = "备注")
    private String remark;

    @Schema(description = "租户号")
    private Long tenantId;

    @Schema(description = "逻辑删除标记：0-正常，1-已删除")
    private Integer delFlag;
}
