package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.supplier.MstSupplierBusinessContactDTO;
import com.meta.supplychain.entity.po.md.MstSupplierBusinessContactPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 商家经营联系人转换器
 */
@Mapper
public interface MstSupplierBusinessContactConvert {

    MstSupplierBusinessContactConvert INSTANCE = Mappers.getMapper(MstSupplierBusinessContactConvert.class);

    /**
     * PO转DTO
     */
    MstSupplierBusinessContactDTO po2Dto(MstSupplierBusinessContactPO po);

    /**
     * PO列表转DTO列表
     */
    List<MstSupplierBusinessContactDTO> po2DtoList(List<MstSupplierBusinessContactPO> poList);

    /**
     * DTO转PO
     * 忽略BaseEntity审计字段，这些字段由MyBatis-Plus自动填充
     */
    @Mapping(target = "createCode", ignore = true)
    @Mapping(target = "createUid", ignore = true)
    @Mapping(target = "createName", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateUid", ignore = true)
    @Mapping(target = "updateCode", ignore = true)
    @Mapping(target = "updateName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    MstSupplierBusinessContactPO dto2Po(MstSupplierBusinessContactDTO dto);
}
