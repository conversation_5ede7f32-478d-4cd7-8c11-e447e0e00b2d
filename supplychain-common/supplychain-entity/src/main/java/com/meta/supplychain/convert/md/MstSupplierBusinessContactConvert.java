package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.supplier.MstSupplierBusinessContactDTO;
import com.meta.supplychain.entity.po.md.MstSupplierBusinessContactPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 商家经营联系人转换器
 */
@Mapper
public interface MstSupplierBusinessContactConvert {

    MstSupplierBusinessContactConvert INSTANCE = Mappers.getMapper(MstSupplierBusinessContactConvert.class);

    /**
     * PO转DTO
     */
    MstSupplierBusinessContactDTO po2Dto(MstSupplierBusinessContactPO po);

    /**
     * PO列表转DTO列表
     */
    List<MstSupplierBusinessContactDTO> po2DtoList(List<MstSupplierBusinessContactPO> poList);

    /**
     * DTO转PO
     */
    MstSupplierBusinessContactPO dto2Po(MstSupplierBusinessContactDTO dto);
}
