package com.meta.supplychain.entity.po.md;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meta.supplychain.entity.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 商家业务联系人信息表
 * @TableName mst_supplier_business_contact
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "mst_supplier_business_contact")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MstSupplierBusinessContactPO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 商家code
     */
    private Long supplierCode;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人职务
     */
    private String contactPosition;

    /**
     * 联系人手机号
     */
    private String contactMobile;

    /**
     * 联系人办公电话
     */
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    private String contactEmail;

    /**
     * 联系人身份证号
     */
    private String contactIdCard;

    /**
     * 备注
     */
    private String remark;

    /**
     * 租户号
     */
    private Long tenantId;

    /**
     * 逻辑删除标记：0-正常，1-已删除
     */
    private Integer delFlag;
}
