package com.meta.supplychain.common.component.domain.md.impl;

import cn.linkkids.framework.croods.common.PageResult;
import com.meta.supplychain.exceptions.ScBizException;
import com.meta.supplychain.enums.md.MdErrorCodeEnum;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meta.supplychain.common.component.domain.md.intf.IMdSupplierDomainService;
import com.meta.supplychain.convert.md.MstSupplierBankAccountConvert;
import com.meta.supplychain.convert.md.MstSupplierBusinessBrandConvert;
import com.meta.supplychain.convert.md.MstSupplierBusinessCategoryConvert;
import com.meta.supplychain.convert.md.MstSupplierBusinessContactConvert;
import com.meta.supplychain.convert.md.MstSupplierConvert;
import com.meta.supplychain.entity.dto.md.req.supplier.MstSupplierPageQueryReq;
import com.meta.supplychain.entity.dto.md.supplier.MstSupplierDTO;
import com.meta.supplychain.entity.po.md.MstSupplierPO;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierBankAccountRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierBusinessBrandRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierBusinessCategoryRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierBusinessContactRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierCategoryRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierRepositoryService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 供应商相关业务接口
 */
@Service
public class MdSupplierDomainServiceImpl implements IMdSupplierDomainService {

    @Resource
    private IMstSupplierRepositoryService supplierRepositoryService;

    @Resource
    private IMstSupplierCategoryRepositoryService categoryRepositoryService;

    @Resource
    private IMstSupplierBankAccountRepositoryService bankAccountRepositoryService;

    @Resource
    private IMstSupplierBusinessBrandRepositoryService businessBrandRepositoryService;

    @Resource
    private IMstSupplierBusinessCategoryRepositoryService businessCategoryRepositoryService;

    @Resource
    private IMstSupplierBusinessContactRepositoryService businessContactRepositoryService;

    @Override
    public PageResult<MstSupplierDTO> pageQuerySupplier(MstSupplierPageQueryReq request) {
        Page<MstSupplierPO> pageResult = supplierRepositoryService.pageQuerySupplier(new Page<>(request.getCurrent(), request.getPageSize()), request);

        List<MstSupplierDTO> result = pageResult.getRecords().stream()
                .map(MstSupplierConvert.INSTANCE::po2Dto)
                .collect(Collectors.toList());

        return PageResult.of(pageResult.getTotal(), result);
    }

    @Override
    public MstSupplierDTO querySupplierDetail(String code) {
        if (!StringUtils.hasText(code)) {
            throw new ScBizException(MdErrorCodeEnum.SCMD007P001);
        }
        MstSupplierPO supplierPO = supplierRepositoryService.getOne(Wrappers.<MstSupplierPO>lambdaQuery().eq(MstSupplierPO::getCode, code));
        if (supplierPO == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD007B002, new Object[]{code});
        }

        MstSupplierDTO supplierDTO = MstSupplierConvert.INSTANCE.po2Dto(supplierPO);

        // 银行账户信息
        supplierDTO.setBankAccounts(
                MstSupplierBankAccountConvert.INSTANCE.po2DtoList(
                        bankAccountRepositoryService.listBySupplierCode(code)
                )
        );

        // 经营品牌信息
        supplierDTO.setBusinessBrands(
                MstSupplierBusinessBrandConvert.INSTANCE.po2DtoList(
                        businessBrandRepositoryService.listBySupplierCode(code)
                )
        );

        // 经营品类信息
        supplierDTO.setBusinessCategories(
                MstSupplierBusinessCategoryConvert.INSTANCE.po2DtoList(
                        businessCategoryRepositoryService.listBySupplierCode(code)
                )
        );

        // 联系人信息
        supplierDTO.setBusinessContacts(
                MstSupplierBusinessContactConvert.INSTANCE.po2DtoList(
                        businessContactRepositoryService.listBySupplierCode(code)
                )
        );

        return supplierDTO;
    }
}
