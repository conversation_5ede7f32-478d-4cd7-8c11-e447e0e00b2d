package com.meta.supplychain.common.component.domain.md.impl;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meta.supplychain.common.component.domain.md.intf.IMdSupplierDomainService;
import com.meta.supplychain.convert.md.MstSupplierBankAccountConvert;
import com.meta.supplychain.convert.md.MstSupplierBusinessBrandConvert;
import com.meta.supplychain.convert.md.MstSupplierBusinessCategoryConvert;
import com.meta.supplychain.convert.md.MstSupplierBusinessContactConvert;
import com.meta.supplychain.convert.md.MstSupplierConvert;
import com.meta.supplychain.entity.dto.md.req.supplier.MstSupplierPageQueryReq;
import com.meta.supplychain.entity.dto.md.supplier.MstSupplierDTO;
import com.meta.supplychain.entity.po.md.MstSupplierPO;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierBankAccountRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierBusinessBrandRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierBusinessCategoryRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierBusinessContactRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierCategoryRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierRepositoryService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 供应商相关业务接口
 */
@Service
public class MdSupplierDomainServiceImpl implements IMdSupplierDomainService {

    @Resource
    private IMstSupplierRepositoryService supplierRepositoryService;

    @Resource
    private IMstSupplierCategoryRepositoryService categoryRepositoryService;

    @Resource
    private IMstSupplierBankAccountRepositoryService bankAccountRepositoryService;

    @Resource
    private IMstSupplierBusinessBrandRepositoryService businessBrandRepositoryService;

    @Resource
    private IMstSupplierBusinessCategoryRepositoryService businessCategoryRepositoryService;

    @Resource
    private IMstSupplierBusinessContactRepositoryService businessContactRepositoryService;

    @Override
    public PageResult<MstSupplierDTO> pageQuerySupplier(MstSupplierPageQueryReq request) {
        Page<MstSupplierPO> pageResult = supplierRepositoryService.pageQuerySupplier(new Page<>(request.getCurrent(), request.getPageSize()), request);

        List<MstSupplierDTO> result = pageResult.getRecords().stream()
                .map(MstSupplierConvert.INSTANCE::po2Dto)
                .collect(Collectors.toList());

        return PageResult.of(pageResult.getTotal(), result);
    }

    @Override
    public MstSupplierDTO querySupplierDetail(String code) {
        // 参数校验
        if (!StringUtils.hasText(code)) {
            BizExceptions.throwWithCodeAndMsg("SUPPLIER_CODE_EMPTY", "供应商编码不能为空");
        }

        // 1. 先查询主表信息
        MstSupplierPO supplierPO = supplierRepositoryService.getOne(
                new LambdaQueryWrapper<MstSupplierPO>()
                        .eq(MstSupplierPO::getCode, code)
                        .eq(MstSupplierPO::getDelFlag, 0)
        );

        if (supplierPO == null) {
            BizExceptions.throwWithCodeAndMsg("SUPPLIER_NOT_FOUND", "供应商不存在，编码：" + code);
        }

        // 2. 转换为DTO
        MstSupplierDTO supplierDTO = MstSupplierConvert.INSTANCE.po2Dto(supplierPO);

        // 3. 查询关联表信息
        try {
            // 查询银行账户信息
            supplierDTO.setBankAccounts(
                    MstSupplierBankAccountConvert.INSTANCE.po2DtoList(
                            bankAccountRepositoryService.listBySupplierCode(code)
                    )
            );

            // 查询经营品牌信息
            supplierDTO.setBusinessBrands(
                    MstSupplierBusinessBrandConvert.INSTANCE.po2DtoList(
                            businessBrandRepositoryService.listBySupplierCode(code)
                    )
            );

            // 查询经营品类信息
            supplierDTO.setBusinessCategories(
                    MstSupplierBusinessCategoryConvert.INSTANCE.po2DtoList(
                            businessCategoryRepositoryService.listBySupplierCode(code)
                    )
            );

            // 查询联系人信息
            supplierDTO.setBusinessContacts(
                    MstSupplierBusinessContactConvert.INSTANCE.po2DtoList(
                            businessContactRepositoryService.listBySupplierCode(code)
                    )
            );
        } catch (Exception e) {
            // 关联表查询失败不影响主信息返回，记录日志即可
            // 这里可以添加日志记录
        }

        return supplierDTO;
    }
}
